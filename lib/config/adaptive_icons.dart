import 'package:flutter/material.dart';
import 'package:flutter/cupertino.dart';
import 'platform_adaptations.dart';

/// Platform-specific icon system for Dasso Reader
///
/// Provides platform-appropriate icons that enhance the native feel
/// while maintaining consistency with the app's design system.
class AdaptiveIcons {
  // =====================================================
  // NAVIGATION ICONS
  // =====================================================

  /// Platform-appropriate back icon
  static IconData get back {
    // Use chevron style back button like in profile header navigation
    return Icons.chevron_left;
  }

  /// Platform-appropriate forward icon
  static IconData get forward {
    if (PlatformAdaptations.isIOS) {
      return CupertinoIcons.forward;
    }
    return Icons.arrow_forward;
  }

  /// Platform-appropriate close icon
  static IconData get close {
    if (PlatformAdaptations.isIOS) {
      return CupertinoIcons.xmark;
    }
    return Icons.close;
  }

  /// Platform-appropriate menu icon
  static IconData get menu {
    if (PlatformAdaptations.isIOS) {
      return CupertinoIcons.line_horizontal_3;
    }
    return Icons.menu;
  }

  /// Platform-appropriate more options icon
  static IconData get moreOptions {
    if (PlatformAdaptations.isIOS) {
      return CupertinoIcons.ellipsis;
    }
    return Icons.more_vert;
  }

  // =====================================================
  // ACTION ICONS
  // =====================================================

  /// Platform-appropriate add icon
  static IconData get add {
    if (PlatformAdaptations.isIOS) {
      return CupertinoIcons.add;
    }
    return Icons.add;
  }

  /// Platform-appropriate delete icon
  static IconData get delete {
    if (PlatformAdaptations.isIOS) {
      return CupertinoIcons.delete;
    }
    return Icons.delete_outline;
  }

  /// Platform-appropriate edit icon
  static IconData get edit {
    if (PlatformAdaptations.isIOS) {
      return CupertinoIcons.pencil;
    }
    return Icons.edit_outlined;
  }

  /// Platform-appropriate save icon
  static IconData get save {
    if (PlatformAdaptations.isIOS) {
      return CupertinoIcons.checkmark;
    }
    return Icons.save_outlined;
  }

  /// Platform-appropriate share icon
  static IconData get share {
    if (PlatformAdaptations.isIOS) {
      return CupertinoIcons.share;
    }
    return Icons.share_outlined;
  }

  /// Platform-appropriate copy icon
  static IconData get copy {
    if (PlatformAdaptations.isIOS) {
      return CupertinoIcons.doc_on_doc;
    }
    return Icons.copy_outlined;
  }

  // =====================================================
  // CONTENT ICONS
  // =====================================================

  /// Platform-appropriate search icon
  static IconData get search {
    if (PlatformAdaptations.isIOS) {
      return CupertinoIcons.search;
    }
    return Icons.search;
  }

  /// Platform-appropriate book icon
  static IconData get book {
    if (PlatformAdaptations.isIOS) {
      return CupertinoIcons.book;
    }
    return Icons.menu_book_outlined;
  }

  /// Platform-appropriate bookmark icon
  static IconData get bookmark {
    if (PlatformAdaptations.isIOS) {
      return CupertinoIcons.bookmark;
    }
    return Icons.bookmark_outline;
  }

  /// Platform-appropriate bookmark filled icon
  static IconData get bookmarkFilled {
    if (PlatformAdaptations.isIOS) {
      return CupertinoIcons.bookmark_fill;
    }
    return Icons.bookmark;
  }

  /// Platform-appropriate note icon
  static IconData get note {
    if (PlatformAdaptations.isIOS) {
      return CupertinoIcons.doc_text;
    }
    return Icons.note_outlined;
  }

  // =====================================================
  // MEDIA ICONS
  // =====================================================

  /// Platform-appropriate play icon
  static IconData get play {
    if (PlatformAdaptations.isIOS) {
      return CupertinoIcons.play_fill;
    }
    return Icons.play_arrow;
  }

  /// Platform-appropriate pause icon
  static IconData get pause {
    if (PlatformAdaptations.isIOS) {
      return CupertinoIcons.pause_fill;
    }
    return Icons.pause;
  }

  /// Platform-appropriate stop icon
  static IconData get stop {
    if (PlatformAdaptations.isIOS) {
      return CupertinoIcons.stop_fill;
    }
    return Icons.stop;
  }

  /// Platform-appropriate volume icon
  static IconData get volume {
    if (PlatformAdaptations.isIOS) {
      return CupertinoIcons.speaker_2;
    }
    return Icons.volume_up_outlined;
  }

  // =====================================================
  // SETTINGS ICONS
  // =====================================================

  /// Platform-appropriate settings icon
  static IconData get settings {
    // Use Material icons for both platforms as they work consistently
    return Icons.settings_outlined;
  }

  /// Platform-appropriate profile icon
  static IconData get profile {
    if (PlatformAdaptations.isIOS) {
      return CupertinoIcons.person;
    }
    return Icons.person_outline;
  }

  /// Platform-appropriate info icon
  static IconData get info {
    if (PlatformAdaptations.isIOS) {
      return CupertinoIcons.info;
    }
    return Icons.info_outline;
  }

  // =====================================================
  // STATUS ICONS
  // =====================================================

  /// Platform-appropriate success icon
  static IconData get success {
    if (PlatformAdaptations.isIOS) {
      return CupertinoIcons.checkmark_circle_fill;
    }
    return Icons.check_circle;
  }

  /// Platform-appropriate error icon
  static IconData get error {
    if (PlatformAdaptations.isIOS) {
      return CupertinoIcons.exclamationmark_circle_fill;
    }
    return Icons.error;
  }

  /// Platform-appropriate warning icon
  static IconData get warning {
    if (PlatformAdaptations.isIOS) {
      return CupertinoIcons.exclamationmark_triangle_fill;
    }
    return Icons.warning;
  }

  // =====================================================
  // LEARNING ICONS (Chinese Learning Specific)
  // =====================================================

  /// Platform-appropriate bookshelf icon
  static IconData get bookshelf {
    if (PlatformAdaptations.isIOS) {
      return CupertinoIcons.book;
    }
    return Icons.book_outlined;
  }

  /// Platform-appropriate bookshelf filled icon
  static IconData get bookshelfFilled {
    if (PlatformAdaptations.isIOS) {
      return CupertinoIcons.book_fill;
    }
    return Icons.book;
  }

  /// Platform-appropriate dictionary icon
  static IconData get dictionary {
    if (PlatformAdaptations.isIOS) {
      return CupertinoIcons.textformat;
    }
    return Icons.translate;
  }

  /// Platform-appropriate dictionary filled icon
  static IconData get dictionaryFilled {
    if (PlatformAdaptations.isIOS) {
      return CupertinoIcons.textformat_alt;
    }
    return Icons.translate;
  }

  /// Platform-appropriate vocabulary icon
  static IconData get vocabulary {
    if (PlatformAdaptations.isIOS) {
      return CupertinoIcons.book;
    }
    return Icons.menu_book; // Changed from rounded to standard for consistency
  }

  /// Platform-appropriate vocabulary filled icon
  static IconData get vocabularyFilled {
    if (PlatformAdaptations.isIOS) {
      return CupertinoIcons.book_fill;
    }
    return Icons.menu_book;
  }

  /// Platform-appropriate HSK icon
  static IconData get hsk {
    if (PlatformAdaptations.isIOS) {
      return CupertinoIcons.book_circle;
    }
    return Icons.school; // Changed from rounded to standard for consistency
  }

  /// Platform-appropriate HSK filled icon
  static IconData get hskFilled {
    if (PlatformAdaptations.isIOS) {
      return CupertinoIcons.book_circle_fill;
    }
    return Icons.school;
  }

  /// Platform-appropriate notes icon (renamed to avoid conflict)
  static IconData get notes {
    if (PlatformAdaptations.isIOS) {
      return CupertinoIcons.doc_text;
    }
    return Icons.note_outlined;
  }

  /// Platform-appropriate notes filled icon
  static IconData get notesFilled {
    if (PlatformAdaptations.isIOS) {
      return CupertinoIcons.doc_text_fill;
    }
    return Icons.note;
  }

  /// Platform-appropriate audio icon
  static IconData get audio {
    if (PlatformAdaptations.isIOS) {
      return CupertinoIcons.speaker_1;
    }
    return Icons.volume_up;
  }

  // =====================================================
  // ADDITIONAL ADAPTIVE ICONS
  // =====================================================

  /// Platform-appropriate text fields icon
  static IconData get textFields {
    if (PlatformAdaptations.isIOS) {
      return CupertinoIcons.textformat;
    }
    return Icons.text_fields;
  }

  /// Platform-appropriate text fields outlined icon
  static IconData get textFieldsOutlined {
    if (PlatformAdaptations.isIOS) {
      return CupertinoIcons.textformat;
    }
    return Icons.text_fields_outlined;
  }

  /// Platform-appropriate content cut icon (for segmentation)
  static IconData get contentCut {
    if (PlatformAdaptations.isIOS) {
      return CupertinoIcons.scissors;
    }
    return Icons.content_cut;
  }

  /// Platform-appropriate help outline icon
  static IconData get helpOutline {
    if (PlatformAdaptations.isIOS) {
      return CupertinoIcons.question_circle;
    }
    return Icons.help_outline;
  }

  /// Platform-appropriate auto fix high outlined icon
  static IconData get autoFixHighOutlined {
    if (PlatformAdaptations.isIOS) {
      return CupertinoIcons.wand_stars;
    }
    return Icons.auto_fix_high_outlined;
  }

  /// Platform-appropriate edit note rounded icon
  static IconData get editNoteRounded {
    if (PlatformAdaptations.isIOS) {
      return CupertinoIcons.pencil_outline;
    }
    return Icons.edit_note_rounded;
  }

  /// Platform-appropriate file download outlined icon
  static IconData get fileDownloadOutlined {
    if (PlatformAdaptations.isIOS) {
      return CupertinoIcons.cloud_download;
    }
    return Icons.file_download_outlined;
  }

  /// Platform-appropriate iOS share icon
  static IconData get iosShare {
    if (PlatformAdaptations.isIOS) {
      return CupertinoIcons.share;
    }
    return Icons.ios_share;
  }

  /// Platform-appropriate menu book rounded icon
  static IconData get menuBookRounded {
    if (PlatformAdaptations.isIOS) {
      return CupertinoIcons.book_fill;
    }
    return Icons.menu_book_rounded;
  }

  /// Platform-appropriate delete outline icon
  static IconData get deleteOutline {
    if (PlatformAdaptations.isIOS) {
      return CupertinoIcons.delete;
    }
    return Icons.delete_outline;
  }

  /// Platform-appropriate search rounded icon
  static IconData get searchRounded {
    if (PlatformAdaptations.isIOS) {
      return CupertinoIcons.search;
    }
    return Icons.search_rounded;
  }

  /// Platform-appropriate clear icon
  static IconData get clear {
    if (PlatformAdaptations.isIOS) {
      return CupertinoIcons.clear;
    }
    return Icons.clear;
  }

  /// Platform-appropriate info outline icon
  static IconData get infoOutline {
    if (PlatformAdaptations.isIOS) {
      return CupertinoIcons.info_circle;
    }
    return Icons.info_outline;
  }

  /// Platform-appropriate translate icon
  static IconData get translate {
    if (PlatformAdaptations.isIOS) {
      return CupertinoIcons.textformat_alt;
    }
    return Icons.translate;
  }

  /// Platform-appropriate error outline icon
  static IconData get errorOutline {
    if (PlatformAdaptations.isIOS) {
      return CupertinoIcons.exclamationmark_circle;
    }
    return Icons.error_outline;
  }

  /// Platform-appropriate arrow back icon
  static IconData get arrowBack {
    if (PlatformAdaptations.isIOS) {
      return CupertinoIcons.back;
    }
    return Icons.arrow_back;
  }

  /// Platform-appropriate arrow forward icon
  static IconData get arrowForward {
    if (PlatformAdaptations.isIOS) {
      return CupertinoIcons.forward;
    }
    return Icons.arrow_forward;
  }

  /// Platform-appropriate auto awesome icon
  static IconData get autoAwesome {
    if (PlatformAdaptations.isIOS) {
      return CupertinoIcons.sparkles;
    }
    return Icons.auto_awesome;
  }

  /// Platform-appropriate sync icon
  static IconData get sync {
    if (PlatformAdaptations.isIOS) {
      return CupertinoIcons.arrow_2_circlepath;
    }
    return Icons.sync;
  }

  /// Platform-appropriate bar chart icon
  static IconData get barChart {
    if (PlatformAdaptations.isIOS) {
      return CupertinoIcons.chart_bar;
    }
    return Icons.bar_chart;
  }

  /// Platform-appropriate color lens icon
  static IconData get colorLens {
    if (PlatformAdaptations.isIOS) {
      return CupertinoIcons.paintbrush;
    }
    return Icons.color_lens;
  }

  /// Platform-appropriate more horizontal icon
  static IconData get moreHoriz {
    if (PlatformAdaptations.isIOS) {
      return CupertinoIcons.ellipsis;
    }
    return Icons.more_horiz;
  }

  /// Platform-appropriate verified icon
  static IconData get verified {
    if (PlatformAdaptations.isIOS) {
      return CupertinoIcons.checkmark_seal_fill;
    }
    return Icons.verified;
  }

  /// Platform-appropriate access time icon
  static IconData get accessTime {
    if (PlatformAdaptations.isIOS) {
      return CupertinoIcons.clock;
    }
    return Icons.access_time;
  }

  /// Platform-appropriate timer off icon
  static IconData get timerOff {
    if (PlatformAdaptations.isIOS) {
      return CupertinoIcons.timer;
    }
    return Icons.timer_off;
  }

  /// Platform-appropriate stars icon
  static IconData get stars {
    if (PlatformAdaptations.isIOS) {
      return CupertinoIcons.star_fill;
    }
    return Icons.stars;
  }

  /// Platform-appropriate refresh icon
  static IconData get refresh {
    if (PlatformAdaptations.isIOS) {
      return CupertinoIcons.refresh;
    }
    return Icons.refresh;
  }

  /// Platform-appropriate iOS-style back arrow
  static IconData get arrowBackIos {
    if (PlatformAdaptations.isIOS) {
      return CupertinoIcons.back;
    }
    return Icons.arrow_back_ios;
  }

  /// Platform-appropriate iOS-style forward arrow
  static IconData get arrowForwardIos {
    if (PlatformAdaptations.isIOS) {
      return CupertinoIcons.forward;
    }
    return Icons.arrow_forward_ios;
  }

  /// Platform-appropriate question mark icon
  static IconData get questionMark {
    if (PlatformAdaptations.isIOS) {
      return CupertinoIcons.question;
    }
    return Icons.question_mark;
  }

  /// Platform-appropriate format underline icon
  static IconData get formatUnderline {
    if (PlatformAdaptations.isIOS) {
      return CupertinoIcons.underline;
    }
    return Icons.format_underline;
  }

  /// Platform-appropriate note alt icon
  static IconData get noteAlt {
    if (PlatformAdaptations.isIOS) {
      return CupertinoIcons.doc_text;
    }
    return Icons.note_alt;
  }

  /// Platform-appropriate note alt outlined icon
  static IconData get noteAltOutlined {
    if (PlatformAdaptations.isIOS) {
      return CupertinoIcons.doc_text;
    }
    return Icons.note_alt_outlined;
  }

  /// Platform-appropriate brush icon
  static IconData get brush {
    if (PlatformAdaptations.isIOS) {
      return CupertinoIcons.paintbrush;
    }
    return Icons.brush;
  }

  /// Platform-appropriate arrow drop down icon
  static IconData get arrowDropDown {
    if (PlatformAdaptations.isIOS) {
      return CupertinoIcons.chevron_down;
    }
    return Icons.arrow_drop_down;
  }

  /// Platform-appropriate library books outlined icon
  static IconData get libraryBooksOutlined {
    if (PlatformAdaptations.isIOS) {
      return CupertinoIcons.book;
    }
    return Icons.library_books_outlined;
  }

  /// Platform-appropriate search off icon
  static IconData get searchOff {
    if (PlatformAdaptations.isIOS) {
      return CupertinoIcons.search;
    }
    return Icons.search_off;
  }

  /// Platform-appropriate bookmark border icon
  static IconData get bookmarkBorder {
    if (PlatformAdaptations.isIOS) {
      return CupertinoIcons.bookmark;
    }
    return Icons.bookmark_border;
  }

  /// Platform-appropriate history icon
  static IconData get history {
    if (PlatformAdaptations.isIOS) {
      return CupertinoIcons.clock;
    }
    return Icons.history;
  }

  /// Platform-appropriate wifi off icon
  static IconData get wifiOff {
    if (PlatformAdaptations.isIOS) {
      return CupertinoIcons.wifi_slash;
    }
    return Icons.wifi_off;
  }

  /// Platform-appropriate lock outline icon
  static IconData get lockOutline {
    if (PlatformAdaptations.isIOS) {
      return CupertinoIcons.lock;
    }
    return Icons.lock_outline;
  }

  /// Platform-appropriate inbox outlined icon
  static IconData get inboxOutlined {
    if (PlatformAdaptations.isIOS) {
      return CupertinoIcons.tray;
    }
    return Icons.inbox_outlined;
  }

  /// Platform-appropriate grid view icon
  static IconData get gridView {
    if (PlatformAdaptations.isIOS) {
      return CupertinoIcons.square_grid_2x2;
    }
    return Icons.grid_view;
  }

  /// Platform-appropriate circle outlined icon
  static IconData get circleOutlined {
    if (PlatformAdaptations.isIOS) {
      return CupertinoIcons.circle;
    }
    return Icons.circle_outlined;
  }

  /// Platform-appropriate circle icon
  static IconData get circle {
    if (PlatformAdaptations.isIOS) {
      return CupertinoIcons.circle_fill;
    }
    return Icons.circle;
  }

  /// Platform-appropriate table of contents icon
  static IconData get toc {
    if (PlatformAdaptations.isIOS) {
      return CupertinoIcons.list_bullet;
    }
    return Icons.toc;
  }

  /// Platform-appropriate sunny outlined icon
  static IconData get wbSunnyOutlined {
    if (PlatformAdaptations.isIOS) {
      return CupertinoIcons.sun_max;
    }
    return Icons.wb_sunny_outlined;
  }

  /// Platform-appropriate auto stories outlined icon
  static IconData get autoStoriesOutlined {
    if (PlatformAdaptations.isIOS) {
      return CupertinoIcons.book;
    }
    return Icons.auto_stories_outlined;
  }

  // =====================================================
  // UTILITY METHODS
  // =====================================================

  /// Creates platform-appropriate icon widget
  static Widget createAdaptiveIcon(
    IconData iconData, {
    double? size,
    Color? color,
  }) {
    return Icon(
      iconData,
      size: size,
      color: color,
    );
  }

  /// Creates platform-appropriate icon button
  static Widget createAdaptiveIconButton({
    required IconData icon,
    required VoidCallback onPressed,
    double? size,
    Color? color,
    String? tooltip,
  }) {
    return IconButton(
      icon: Icon(icon),
      onPressed: onPressed,
      iconSize: size,
      color: color,
      tooltip: tooltip,
    );
  }

  /// Returns platform-appropriate chevron icon for navigation
  static IconData get chevronRight {
    // Use Material icons for both platforms as they work consistently
    return Icons.chevron_right;
  }

  /// Returns platform-appropriate chevron icon for dropdowns
  static IconData get chevronDown {
    if (PlatformAdaptations.isIOS) {
      return CupertinoIcons.chevron_down;
    }
    return Icons.keyboard_arrow_down;
  }

  /// Returns platform-appropriate home icon
  static IconData get home {
    if (PlatformAdaptations.isIOS) {
      return CupertinoIcons.house;
    }
    return Icons.home_outlined;
  }
}
